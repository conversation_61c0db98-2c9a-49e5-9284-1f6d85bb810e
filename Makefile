# Makefile generated by <PERSON><PERSON><PERSON><PERSON>enerator.cs
# *DO NOT EDIT*

UNREALROOTPATH = /opt/UnrealEngine5
GAMEPROJECTFILE =/home/<USER>/code/Unreal Engine/RealmOfWar/RealmOfWar.uproject

TARGETS = \
	RealmOfWar-Linux-DebugGame  \
	RealmOfWar-Linux-Shipping  \
	RealmOfWar \
	RealmOfWarEditor-Linux-DebugGame  \
	RealmOfWarEditor-Linux-Shipping  \
	RealmOfWarEditor \
	UnrealEditor-Linux-DebugGame  \
	UnrealEditor-Linux-Shipping  \
	UnrealEditor \
	UnrealGame-Linux-DebugGame  \
	UnrealGame-Linux-Shipping  \
	UnrealGame\
	configure

BUILD = bash "$(UNREALROOTPATH)/Engine/Build/BatchFiles/Linux/Build.sh"
PROJECTBUILD = "$(UNREALROOTPATH)/Engine/Binaries/ThirdParty/DotNet/6.0.302/linux/dotnet" "$(UNREALROOTPATH)/Engine/Binaries/DotNET/UnrealBuildTool/UnrealBuildTool.dll"

all: StandardSet

RequiredTools: CrashReportClient-Linux-Shipping CrashReportClientEditor-Linux-Shipping ShaderCompileWorker UnrealLightmass EpicWebHelper-Linux-Shipping

StandardSet: RequiredTools UnrealFrontend RealmOfWarEditor UnrealInsights

DebugSet: RequiredTools UnrealFrontend-Linux-Debug RealmOfWarEditor-Linux-Debug


RealmOfWar-Linux-DebugGame:
	 $(PROJECTBUILD) RealmOfWar Linux DebugGame  -project="$(GAMEPROJECTFILE)" $(ARGS)

RealmOfWar-Linux-Shipping:
	 $(PROJECTBUILD) RealmOfWar Linux Shipping  -project="$(GAMEPROJECTFILE)" $(ARGS)

RealmOfWar:
	 $(PROJECTBUILD) RealmOfWar Linux Development  -project="$(GAMEPROJECTFILE)" $(ARGS)

RealmOfWarEditor-Linux-DebugGame:
	 $(PROJECTBUILD) RealmOfWarEditor Linux DebugGame  -project="$(GAMEPROJECTFILE)" $(ARGS)

RealmOfWarEditor-Linux-Shipping:
	 $(PROJECTBUILD) RealmOfWarEditor Linux Shipping  -project="$(GAMEPROJECTFILE)" $(ARGS)

RealmOfWarEditor:
	 $(PROJECTBUILD) RealmOfWarEditor Linux Development  -project="$(GAMEPROJECTFILE)" $(ARGS)

UnrealEditor-Linux-DebugGame:
	 $(BUILD) UnrealEditor Linux DebugGame  -project="$(GAMEPROJECTFILE)" $(ARGS)

UnrealEditor-Linux-Shipping:
	 $(BUILD) UnrealEditor Linux Shipping  -project="$(GAMEPROJECTFILE)" $(ARGS)

UnrealEditor:
	 $(BUILD) UnrealEditor Linux Development  -project="$(GAMEPROJECTFILE)" $(ARGS)

UnrealGame-Linux-DebugGame:
	 $(BUILD) UnrealGame Linux DebugGame  -project="$(GAMEPROJECTFILE)" $(ARGS)

UnrealGame-Linux-Shipping:
	 $(BUILD) UnrealGame Linux Shipping  -project="$(GAMEPROJECTFILE)" $(ARGS)

UnrealGame:
	 $(BUILD) UnrealGame Linux Development  -project="$(GAMEPROJECTFILE)" $(ARGS)

configure:
	xbuild /property:Configuration=Development /verbosity:quiet /nologo "$(UNREALROOTPATH)/Engine/Source/Programs/UnrealBuildTool/UnrealBuildTool.csproj"
	$(PROJECTBUILD) -projectfiles -project="\"$(GAMEPROJECTFILE)\"" -game -engine 

.PHONY: $(TARGETS)
